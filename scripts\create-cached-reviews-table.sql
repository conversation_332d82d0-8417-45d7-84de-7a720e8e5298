-- Create cached_reviews table for Google Reviews integration
-- This table stores Google reviews locally for caching and fallback purposes

CREATE TABLE IF NOT EXISTS cached_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    external_id TEXT UNIQUE NOT NULL, -- Google review ID or other external identifier
    author_name TEXT NOT NULL,
    author_initials TEXT NOT NULL,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    text TEXT,
    date TIMESTAMP WITH TIME ZONE NOT NULL,
    relative_time TEXT NOT NULL,
    profile_photo_url TEXT,
    source TEXT NOT NULL CHECK (source IN ('google_places', 'google_business', 'manual')),
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_cached_reviews_source ON cached_reviews(source);
CREATE INDEX IF NOT EXISTS idx_cached_reviews_rating ON cached_reviews(rating);
CREATE INDEX IF NOT EXISTS idx_cached_reviews_is_active ON cached_reviews(is_active);
CREATE INDEX IF NOT EXISTS idx_cached_reviews_date ON cached_reviews(date DESC);
CREATE INDEX IF NOT EXISTS idx_cached_reviews_external_id ON cached_reviews(external_id);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_cached_reviews_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_cached_reviews_updated_at
    BEFORE UPDATE ON cached_reviews
    FOR EACH ROW
    EXECUTE FUNCTION update_cached_reviews_updated_at();

-- Insert some sample reviews for testing
INSERT INTO cached_reviews (external_id, author_name, author_initials, rating, text, date, relative_time, source) VALUES
('sample_1', 'Marie Dubois', 'MD', 5, 'Une expérience magique ! L''équipe est passionnée et les paysages à couper le souffle.', NOW() - INTERVAL '2 weeks', 'il y a 2 semaines', 'manual'),
('sample_2', 'Jean-Luc Martin', 'JM', 5, 'Parfait pour découvrir la Guadeloupe autrement. Nos enfants ont adoré la chasse au trésor !', NOW() - INTERVAL '3 weeks', 'il y a 3 semaines', 'manual'),
('sample_3', 'Sophie Leroy', 'SL', 4, 'Très belle sortie en famille. Le guide était très sympa et connaissait bien la région.', NOW() - INTERVAL '1 month', 'il y a 1 mois', 'manual'),
('sample_4', 'Pierre Moreau', 'PM', 5, 'Activité incontournable en Guadeloupe ! Les waterbikes sont géniaux et l''équipe au top.', NOW() - INTERVAL '5 weeks', 'il y a 5 semaines', 'manual'),
('sample_5', 'Isabelle Rousseau', 'IR', 4, 'Très bonne organisation, personnel accueillant. Les waterbikes sont vraiment amusants !', NOW() - INTERVAL '2 weeks', 'il y a 2 semaines', 'manual')
ON CONFLICT (external_id) DO NOTHING;

-- Enable Row Level Security (RLS)
ALTER TABLE cached_reviews ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Public can read active reviews
CREATE POLICY "Public can read active reviews" ON cached_reviews
    FOR SELECT USING (is_active = true);

-- Authenticated users can read all reviews
CREATE POLICY "Authenticated users can read all reviews" ON cached_reviews
    FOR SELECT TO authenticated USING (true);

-- Only service role can insert/update/delete
CREATE POLICY "Service role can manage reviews" ON cached_reviews
    FOR ALL TO service_role USING (true);

COMMENT ON TABLE cached_reviews IS 'Stores Google reviews locally for caching and fallback purposes';
COMMENT ON COLUMN cached_reviews.external_id IS 'Unique identifier from external source (Google, etc.)';
COMMENT ON COLUMN cached_reviews.source IS 'Source of the review: google_places, google_business, or manual';
COMMENT ON COLUMN cached_reviews.is_active IS 'Whether the review should be displayed publicly';
