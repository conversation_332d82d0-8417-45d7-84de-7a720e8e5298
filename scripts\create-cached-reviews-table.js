#!/usr/bin/env node

/**
 * <PERSON>rip<PERSON> to create the cached_reviews table for Google Reviews integration
 */

const { createClient } = require("@supabase/supabase-js");
const fs = require("fs");
const path = require("path");

// Read environment variables from .env.local file manually
function loadEnvFile() {
	try {
		const envPath = path.join(__dirname, "..", ".env.local");
		const envContent = fs.readFileSync(envPath, "utf8");
		const envVars = {};

		envContent.split("\n").forEach((line) => {
			const trimmed = line.trim();
			if (trimmed && !trimmed.startsWith("#")) {
				const [key, ...valueParts] = trimmed.split("=");
				if (key && valueParts.length > 0) {
					envVars[key.trim()] = valueParts.join("=").trim();
				}
			}
		});

		return envVars;
	} catch (error) {
		console.error("Error reading .env.local file:", error.message);
		return {};
	}
}

const envVars = loadEnvFile();
const SUPABASE_URL = envVars.NEXT_PUBLIC_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = envVars.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
	console.error("❌ Missing required environment variables:");
	console.error("   NEXT_PUBLIC_SUPABASE_URL:", !!SUPABASE_URL);
	console.error("   SUPABASE_SERVICE_ROLE_KEY:", !!SUPABASE_SERVICE_KEY);
	process.exit(1);
}

async function createCachedReviewsTable() {
	console.log("🚀 Creating cached_reviews table...");

	// Create Supabase client with service role key
	const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
		auth: {
			autoRefreshToken: false,
			persistSession: false,
		},
	});

	try {
		// Read the SQL file
		const sqlPath = path.join(__dirname, "create-cached-reviews-table.sql");
		const sql = fs.readFileSync(sqlPath, "utf8");

		// Split SQL into individual statements
		const statements = sql
			.split(";")
			.map((stmt) => stmt.trim())
			.filter((stmt) => stmt.length > 0 && !stmt.startsWith("--"));

		console.log(`📝 Executing ${statements.length} SQL statements...`);

		// Execute each statement
		for (let i = 0; i < statements.length; i++) {
			const statement = statements[i] + ";";
			console.log(`   ${i + 1}/${statements.length}: ${statement.substring(0, 50)}...`);

			const { error } = await supabase.rpc("exec_sql", { sql_query: statement });

			if (error) {
				// Try direct query if RPC fails
				const { error: directError } = await supabase
					.from("_temp_table_that_does_not_exist")
					.select("*")
					.limit(0);

				// If it's a table creation, try using the raw query
				if (statement.includes("CREATE TABLE")) {
					console.log(`   ⚠️  RPC failed, trying alternative method...`);
					// For table creation, we'll need to use a different approach
					continue;
				} else {
					console.error(`   ❌ Error executing statement ${i + 1}:`, error.message);
					throw error;
				}
			} else {
				console.log(`   ✅ Statement ${i + 1} executed successfully`);
			}
		}

		// Verify the table was created by trying to query it
		console.log("🔍 Verifying table creation...");
		const { data, error } = await supabase.from("cached_reviews").select("count(*)").limit(1);

		if (error) {
			console.error("❌ Table verification failed:", error.message);
			console.log("📋 Manual SQL to execute in Supabase SQL Editor:");
			console.log("=" * 50);
			console.log(sql);
			console.log("=" * 50);
		} else {
			console.log("✅ Table created and verified successfully!");
			console.log(`📊 Current review count: ${data?.[0]?.count || 0}`);
		}
	} catch (error) {
		console.error("❌ Error creating table:", error.message);
		console.log("\n📋 Manual SQL to execute in Supabase SQL Editor:");
		console.log("=".repeat(50));

		const sqlPath = path.join(__dirname, "create-cached-reviews-table.sql");
		const sql = fs.readFileSync(sqlPath, "utf8");
		console.log(sql);
		console.log("=".repeat(50));

		process.exit(1);
	}
}

// Run the script
createCachedReviewsTable()
	.then(() => {
		console.log("🎉 Script completed successfully!");
		process.exit(0);
	})
	.catch((error) => {
		console.error("💥 Script failed:", error.message);
		process.exit(1);
	});
